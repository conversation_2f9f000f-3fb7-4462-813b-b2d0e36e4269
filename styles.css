/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* ===== VARIABLES ===== */
:root {
    /* Colors - Minimalist Black & White */
    --primary-color: #000000;
    --primary-dark: #000000;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-muted: #999999;
    --background: #ffffff;
    --background-alt: #fafafa;
    --border-color: #e0e0e0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.08);
    --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --container-max-width: 1000px;
    --section-padding: 6rem 0;
    --section-padding-mobile: 4rem 0;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;


}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border-radius: 0.5rem;
    cursor: pointer;
    border: 2px solid transparent;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: white;
    color: var(--primary-color);
    box-shadow: none;
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    max-width: var(--container-max-width);
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.nav-logo {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    letter-spacing: 0.025em;
}

.nav-logo:hover {
    opacity: 0.7;
}

.nav-subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.nav-content {
    display: flex;
    align-items: center;
    gap: 3rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 400;
    position: relative;
    padding: 0.5rem 0;
}

.nav-number {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 300;
    min-width: 1.5rem;
}

.nav-text {
    font-size: var(--font-size-sm);
    letter-spacing: 0.025em;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
}

.nav-link:hover .nav-number,
.nav-link.active .nav-number {
    color: var(--text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--text-primary);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    align-items: flex-end;
    cursor: pointer;
    gap: 0.5rem;
}

.nav-toggle-text {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.nav-toggle-lines {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.bar {
    height: 1px;
    background-color: var(--text-primary);
}

.bar:first-child {
    width: 1.5rem;
}

.bar:last-child {
    width: 1rem;
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding-top: 5rem;
}

.hero-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 60vh;
}

.hero-text {
    margin-bottom: 4rem;
}

.hero-name {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
    line-height: 0.9;
}

.hero-divider {
    width: 60px;
    height: 1px;
    background-color: var(--text-primary);
    margin-bottom: 2rem;
}

.hero-title {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: 400;
    margin: 0 0 2rem 0;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.hero-bio {
    font-size: var(--font-size-base);
    color: var(--text-muted);
    font-weight: 300;
    font-style: italic;
    margin: 0;
    letter-spacing: 0.025em;
}

.hero-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
}

.hero-location,
.hero-status {
    letter-spacing: 0.025em;
}

.hero-scroll {
    position: absolute;
    bottom: 3rem;
    right: 0;
}

.scroll-line {
    width: 1px;
    height: 4rem;
    background-color: var(--text-muted);
    position: relative;
    opacity: 1;
}

/* ===== SECTIONS ===== */
section {
    padding: var(--section-padding);
}

.about {
    background-color: var(--background);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

.about-intro {
    position: sticky;
    top: 8rem;
}

.about-title {
    font-size: var(--font-size-4xl);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
}

.about-divider {
    width: 40px;
    height: 1px;
    background-color: var(--text-primary);
}

.about-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.about-paragraph {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

.about-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.detail-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
}

/* ===== SKILLS SECTION ===== */
.skills-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

.skills-intro {
    position: sticky;
    top: 8rem;
}

.skills-title {
    font-size: var(--font-size-4xl);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
}

.skills-divider {
    width: 40px;
    height: 1px;
    background-color: var(--text-primary);
}

.skills-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.skills-text {
    margin-bottom: 1rem;
}

.skills-paragraph {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

.skills-categories {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.skill-category {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
}

.category-title {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    letter-spacing: 0.025em;
}

.skill-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skill-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.skill-item:last-child {
    border-bottom: none;
}

.skill-item:hover {
    padding-left: 1rem;
}

.skill-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    letter-spacing: 0.025em;
}

.skill-level {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== PROJECTS SECTION ===== */
.projects {
    background-color: var(--background);
    border-bottom: 1px solid var(--border-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

.projects-intro {
    position: sticky;
    top: 8rem;
}

.projects-title {
    font-size: var(--font-size-4xl);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
}

.projects-divider {
    width: 40px;
    height: 1px;
    background-color: var(--text-primary);
}

.projects-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.projects-text {
    margin-bottom: 1rem;
}

.projects-paragraph {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

.projects-list {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.project-item {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
}

.project-item:hover {
    padding-left: 1rem;
}

.project-header {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.project-number {
    font-size: var(--font-size-lg);
    font-weight: 300;
    color: var(--text-muted);
    min-width: 3rem;
    letter-spacing: 0.1em;
}

.project-meta {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: 0.025em;
}

.project-year {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    white-space: nowrap;
}

.project-details {
    margin-left: 5rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.project-image {
    position: relative;
    width: 100%;
    max-width: 600px;
    aspect-ratio: 16 / 9;
    background-color: var(--background-alt);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.project-image:hover {
    border-color: var(--text-primary);
}

.project-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    opacity: 1;
}

.project-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 300;
    color: var(--text-muted);
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.1em;
    background-color: var(--background-alt);
    opacity: 0;
}

/* Show placeholder when image fails to load */
.project-img:not([src]),
.project-img[src=""],
.project-img:not([alt]) + .project-placeholder {
    opacity: 1;
}

.project-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
    font-size: var(--font-size-base);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.tech-item {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.project-links {
    display: flex;
    gap: 2rem;
}

.project-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-sm);
    border-bottom: 1px solid transparent;
    letter-spacing: 0.025em;
}

.project-link:hover {
    opacity: 0.7;
    border-bottom-color: var(--text-primary);
}

/* ===== CONTACT SECTION ===== */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

.contact-intro {
    position: sticky;
    top: 8rem;
}

.contact-title {
    font-size: var(--font-size-4xl);
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
}

.contact-divider {
    width: 40px;
    height: 1px;
    background-color: var(--text-primary);
}

.contact-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.contact-text {
    margin-bottom: 1rem;
}

.contact-paragraph {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    margin: 0;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.contact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item:hover {
    padding-left: 1rem;
}

.contact-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.contact-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
    text-decoration: none;
}

.contact-value:hover {
    opacity: 0.7;
}

.contact-availability {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.availability-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.availability-item:last-child {
    border-bottom: none;
}

.availability-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.availability-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--background);
    border-top: 1px solid var(--border-color);
    padding: 4rem 0 2rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-title {
    font-size: var(--font-size-2xl);
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: 0.025em;
}

.footer-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin: 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-section-title {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 400;
    position: relative;
}

.footer-link:hover {
    color: var(--text-primary);
    padding-left: 0.5rem;
}

.footer-text {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    font-weight: 400;
}

.footer-bottom {
    margin-top: 3rem;
}

.footer-divider {
    width: 100%;
    height: 1px;
    background-color: var(--border-color);
    margin-bottom: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-copyright {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 400;
    margin: 0;
}

.footer-top {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 400;
}

.footer-top:hover {
    color: var(--text-primary);
}

.footer-arrow {
    width: 1px;
    height: 1rem;
    background-color: currentColor;
    position: relative;
}

.footer-arrow::after {
    content: '';
    position: absolute;
    top: 0;
    left: -2px;
    width: 5px;
    height: 5px;
    border-top: 1px solid currentColor;
    border-right: 1px solid currentColor;
    transform: rotate(-45deg);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
    }

    section {
        padding: var(--section-padding-mobile);
    }

    .container {
        padding: 0 1rem;
    }

    .nav-container {
        padding: 1rem 1.5rem;
    }

    .nav-brand {
        gap: 0.125rem;
    }

    .nav-logo {
        font-size: var(--font-size-base);
    }

    .nav-content {
        gap: 1.5rem;
    }

    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: var(--background);
        flex-direction: column;
        gap: 0;
        padding: 3rem 2rem;
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        border-top: 1px solid var(--border-color);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-item {
        padding: 1rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .nav-item:last-child {
        border-bottom: none;
    }

    .nav-link {
        padding: 0.5rem 0;
        justify-content: space-between;
        width: 100%;
    }

    .nav-link::after {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .nav-toggle-text {
        color: var(--text-primary);
    }

    .nav-toggle.active .bar:first-child {
        transform: rotate(45deg) translate(2px, 2px);
        width: 1.25rem;
    }

    .nav-toggle.active .bar:last-child {
        transform: rotate(-45deg) translate(2px, -2px);
        width: 1.25rem;
    }

    .hero {
        padding-top: 6rem;
        min-height: 90vh;
        align-items: flex-start;
        padding-top: 8rem;
    }

    .hero-content {
        min-height: 50vh;
    }

    .hero-text {
        margin-bottom: 2rem;
    }

    .hero-name {
        font-size: clamp(2.5rem, 12vw, 4rem);
        margin-bottom: 1.5rem;
    }

    .hero-divider {
        width: 40px;
        margin-bottom: 1.5rem;
    }

    .hero-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .hero-scroll {
        bottom: 2rem;
    }

    .scroll-line {
        height: 3rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .skills-intro {
        position: static;
    }

    .skills-title {
        font-size: var(--font-size-3xl);
        margin-bottom: 1rem;
    }

    .skills-content {
        gap: 2rem;
    }

    .skills-categories {
        gap: 2rem;
    }

    .skill-category {
        padding-top: 1.5rem;
    }

    .skill-item:hover {
        padding-left: 0.5rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .projects-intro {
        position: static;
    }

    .projects-title {
        font-size: var(--font-size-3xl);
        margin-bottom: 1rem;
    }

    .projects-content {
        gap: 2rem;
    }

    .projects-list {
        gap: 2rem;
    }

    .project-item {
        padding-top: 1.5rem;
    }

    .project-item:hover {
        padding-left: 0.5rem;
    }

    .project-header {
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .project-number {
        min-width: 2rem;
        font-size: var(--font-size-base);
    }

    .project-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .project-title {
        font-size: var(--font-size-lg);
    }

    .project-details {
        margin-left: 3rem;
        gap: 1.5rem;
    }

    .project-image {
        max-width: 500px;
    }

    .project-tech {
        gap: 0.75rem;
    }

    .project-links {
        gap: 1.5rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-intro {
        position: static;
    }

    .contact-title {
        font-size: var(--font-size-3xl);
        margin-bottom: 1rem;
    }

    .contact-content {
        gap: 2rem;
    }

    .contact-details,
    .contact-availability {
        padding-top: 1.5rem;
        gap: 1rem;
    }

    .contact-item,
    .availability-item {
        padding: 0.75rem 0;
    }

    .contact-item:hover {
        padding-left: 0.5rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-section-title {
        margin-bottom: 0.75rem;
    }

    .footer-links {
        gap: 0.5rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-top {
        order: -1;
    }
}

@media (max-width: 480px) {
    .hero-name {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .skill-item {
        padding: 1rem;
    }

    .project-content {
        padding: 1.5rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-intro {
        position: static;
    }

    .about-title {
        font-size: var(--font-size-3xl);
        margin-bottom: 1rem;
    }

    .about-content {
        gap: 2rem;
    }

    .about-details {
        padding-top: 1.5rem;
        gap: 1rem;
    }

    .detail-item {
        padding: 0.75rem 0;
    }

    /* Projects mobile improvements */
    .projects-title {
        font-size: var(--font-size-2xl);
    }

    .project-header {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .project-number {
        min-width: auto;
        font-size: var(--font-size-sm);
    }

    .project-meta {
        gap: 0.25rem;
    }

    .project-title {
        font-size: var(--font-size-base);
    }

    .project-year {
        font-size: var(--font-size-xs);
    }

    .project-details {
        margin-left: 0;
        gap: 1rem;
    }

    .project-image {
        max-width: 100%;
    }

    .project-description {
        font-size: var(--font-size-sm);
    }

    .tech-item {
        font-size: 0.625rem;
    }

    .project-links {
        gap: 1rem;
    }

    .project-link {
        font-size: var(--font-size-xs);
    }
}

/* ===== ACCESSIBILITY ===== */

/* Focus styles for keyboard navigation */
.nav-link:focus,
.btn:focus,
.project-link:focus,
.contact-value:focus,
.footer-link:focus {
    outline: none;
}

/* Custom focus styles for better UX */
.nav-link:focus-visible,
.btn:focus-visible,
.project-link:focus-visible,
.contact-value:focus-visible,
.footer-link:focus-visible {
    outline: 1px solid var(--text-primary);
    outline-offset: 2px;
}

/* Remove default focus outline for mouse users */
.nav-link:focus:not(:focus-visible),
.btn:focus:not(:focus-visible),
.project-link:focus:not(:focus-visible),
.contact-value:focus:not(:focus-visible),
.footer-link:focus:not(:focus-visible) {
    outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
}