// ===== PORTFOLIO WEBSITE JAVASCRIPT =====

// DOM Elements
const nav = document.getElementById('nav');
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('section');

// ===== MOBILE NAVIGATION =====
function toggleMobileNav() {
    navMenu.classList.toggle('active');
    navToggle.classList.toggle('active');

    // Prevent body scroll when menu is open
    if (navMenu.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

function closeMobileNav() {
    navMenu.classList.remove('active');
    navToggle.classList.remove('active');
    document.body.style.overflow = '';
}

// Event listeners for mobile navigation
navToggle.addEventListener('click', toggleMobileNav);

// Close mobile nav when clicking on a link
navLinks.forEach(link => {
    link.addEventListener('click', closeMobileNav);
});

// Close mobile nav when clicking outside
document.addEventListener('click', (e) => {
    if (!nav.contains(e.target) && navMenu.classList.contains('active')) {
        closeMobileNav();
    }
});

// ===== INSTANT SCROLLING (SMOOTH SCROLLING REMOVED) =====
function instantScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        const navHeight = nav.offsetHeight;
        const elementPosition = element.offsetTop - navHeight;

        window.scrollTo({
            top: elementPosition,
            behavior: 'auto'
        });
    }
}

// Add instant scrolling to navigation links
navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = link.getAttribute('href');
        if (target.startsWith('#')) {
            instantScroll(target);
        }
    });
});

// Add instant scrolling to scroll indicator
const scrollIndicator = document.querySelector('.scroll-indicator');
if (scrollIndicator) {
    scrollIndicator.addEventListener('click', (e) => {
        e.preventDefault();
        const target = scrollIndicator.getAttribute('href');
        instantScroll(target);
    });
}

// Add instant scrolling to footer "Back to Top" link
const footerTop = document.querySelector('.footer-top');
if (footerTop) {
    footerTop.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'auto'
        });
    });
}

// ===== ACTIVE NAVIGATION HIGHLIGHTING =====
function updateActiveNavLink() {
    const scrollPosition = window.scrollY + nav.offsetHeight + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${sectionId}`) {
                    link.classList.add('active');
                }
            });
        }
    });
}

// ===== NAVBAR SCROLL EFFECT =====
function handleNavbarScroll() {
    // Minimalist design - subtle border change on scroll
    if (window.scrollY > 50) {
        nav.style.borderBottomColor = '#000000';
        nav.style.backgroundColor = 'rgba(255, 255, 255, 1)';
    } else {
        nav.style.borderBottomColor = '#e0e0e0';
        nav.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
    }
}

// ===== SCROLL ANIMATIONS (REMOVED) =====
// All scroll animations have been removed - elements are now immediately visible

// ===== TYPING ANIMATION (REMOVED) =====
// Typing animation has been removed - text appears immediately
function setTextImmediately(element, text) {
    element.innerHTML = text;
}

// ===== SCROLL EVENT LISTENERS =====
function handleScroll() {
    updateActiveNavLink();
    handleNavbarScroll();
}

window.addEventListener('scroll', handleScroll);

// ===== KEYBOARD NAVIGATION =====
document.addEventListener('keydown', (e) => {
    // ESC key closes mobile menu
    if (e.key === 'Escape' && navMenu.classList.contains('active')) {
        closeMobileNav();
    }

    // Arrow keys for navigation (when focused on nav links)
    if (document.activeElement.classList.contains('nav-link')) {
        const currentIndex = Array.from(navLinks).indexOf(document.activeElement);

        if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
            e.preventDefault();
            const nextIndex = (currentIndex + 1) % navLinks.length;
            navLinks[nextIndex].focus();
        } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
            e.preventDefault();
            const prevIndex = (currentIndex - 1 + navLinks.length) % navLinks.length;
            navLinks[prevIndex].focus();
        }
    }
});

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    // Set initial active nav link
    updateActiveNavLink();

    // Set hero text immediately (no typing animation)
    const heroName = document.querySelector('.hero-name');
    if (heroName) {
        const originalText = heroName.textContent;
        setTextImmediately(heroName, originalText);
    }

    // Ensure all hero elements are visible immediately (no animations)
    const heroElements = document.querySelectorAll('.hero-divider, .hero-title, .hero-bio, .hero-meta');
    heroElements.forEach((element) => {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
    });
});

// ===== PERFORMANCE OPTIMIZATION =====
// Debounce function for scroll events (simplified since no animations)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        setTimeout(later, wait);
    };
}

// Use debounced scroll handler for better performance
const debouncedScrollHandler = debounce(handleScroll, 10);
window.removeEventListener('scroll', handleScroll);
window.addEventListener('scroll', debouncedScrollHandler);

// ===== ACCESSIBILITY ENHANCEMENTS =====
// Skip to main content functionality
function addSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--primary-color);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1001;
    `;

    skipLink.addEventListener('focus', () => {
        skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
        skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
}

// Initialize accessibility features
addSkipLink();

// Announce page changes to screen readers
function announcePageChange(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
    `;
    announcement.textContent = message;
    document.body.appendChild(announcement);

    // Remove announcement after 1 second (no animation)
    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

// Announce section changes when navigating
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        const sectionName = link.textContent;
        // Announce immediately (no delay needed without animations)
        announcePageChange(`Navigated to ${sectionName} section`);
    });
});