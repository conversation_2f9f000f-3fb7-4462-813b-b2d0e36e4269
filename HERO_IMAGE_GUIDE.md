# Hero Section Image Setup Guide

## Overview
The hero section has been redesigned to include a 1:1 (square) profile image alongside the text content. The layout is responsive and adapts beautifully across all screen sizes.

## Adding Your Profile Image

### 1. Image Requirements
- **Aspect Ratio**: 1:1 (square)
- **Recommended Size**: 640x640px or higher
- **Format**: JPG, PNG, or WebP
- **File Name**: `profile.jpg` (or update the src in HTML)

### 2. File Location
Place your profile image in the `assets/` folder:
```
assets/
├── profile.jpg          ← Your profile image goes here
├── project-01.png
└── project-02.png
```

### 3. HTML Structure
The image is already set up in the HTML:
```html
<div class="hero-image">
    <div class="hero-image-container">
        <img src="assets/profile.jpg" alt="Pradeep Yadav - Full-Stack Developer" class="hero-img">
        <div class="hero-image-placeholder">PY</div>
    </div>
</div>
```

### 4. Fallback Placeholder
If the image fails to load or doesn't exist, a placeholder with initials "PY" will be displayed instead.

## Layout Details

### Desktop Layout (1024px+)
- **Two-column grid**: Text content on the left, image on the right
- **Image size**: 320x320px display size
- **Gap**: 4rem between text and image

### Tablet Layout (768px - 1024px)
- **Two-column grid**: Maintained but with smaller image
- **Image size**: 280x280px display size
- **Gap**: 3rem between text and image

### Mobile Layout (768px and below)
- **Single column**: Image stacked above text content
- **Image size**: 280x280px (480px: 240x240px)
- **Centered layout**: Both image and text are centered
- **Gap**: 3rem between sections (2rem on small screens)

## Customization Options

### Change Image Size
Edit the CSS in `styles.css`:
```css
.hero-image-container {
    width: 320px;    /* Adjust width */
    height: 320px;   /* Adjust height (keep same as width for 1:1) */
}
```

### Change Border Radius
```css
.hero-image-container {
    border-radius: 8px;    /* Adjust for more/less rounded corners */
    /* border-radius: 50%; for circular image */
}
```

### Update Placeholder Initials
Change the placeholder text in HTML:
```html
<div class="hero-image-placeholder">YI</div>  <!-- Your initials -->
```

### Change Image File
Update the src attribute in HTML:
```html
<img src="assets/your-image.jpg" alt="Your Name - Your Title" class="hero-img">
```

## Responsive Behavior
- **Large screens**: Image appears on the right side
- **Medium screens**: Image size reduces slightly
- **Small screens**: Image moves above text content and centers
- **All sizes**: Maintains 1:1 aspect ratio and proper spacing

## Accessibility
- Proper alt text is included for screen readers
- Focus states are maintained for keyboard navigation
- High contrast support is built-in

## Browser Support
- Works in all modern browsers
- Graceful fallback for older browsers
- CSS Grid with fallback support
