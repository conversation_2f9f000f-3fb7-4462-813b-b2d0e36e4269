# Project Images Guide

## Adding Images to Your Portfolio

### Image Requirements

**Aspect Ratio:** 16:9 (recommended)
**Dimensions:** 1920x1080px or 1600x900px (high quality)
**Format:** JPG or PNG
**File Size:** Keep under 500KB for optimal loading

### File Naming Convention

Place your project images in the `assets/` folder with these names:
- `project-01.jpg` - E-Commerce Platform
- `project-02.jpg` - Task Management App  
- `project-03.jpg` - Analytics Dashboard

### Image Content Suggestions

**For Best Results, Include:**
1. **Clean Screenshots** of your application's main interface
2. **Multiple Views** - combine desktop and mobile views in one image
3. **Key Features** highlighted in the screenshot
4. **Professional Presentation** - clean, well-lit screenshots

### Image Optimization Tips

1. **Use High-Quality Screenshots:**
   - Take screenshots at 2x resolution for crisp display
   - Ensure good contrast and readability
   - Show the most impressive/functional parts of your app

2. **Optimize File Size:**
   - Use tools like TinyPNG or ImageOptim
   - Aim for under 500KB per image
   - Consider WebP format for better compression

3. **Consistent Style:**
   - Use similar screenshot styles across projects
   - Consider adding subtle shadows or borders for consistency
   - Maintain similar color schemes if possible

### Fallback Behavior

If images don't load or aren't available:
- The numbered placeholders (01, 02, 03) will automatically show
- The layout remains intact and professional
- No broken image icons will appear

### Current Image Paths

The HTML is set up to load images from:
```
assets/project-01.jpg
assets/project-02.jpg  
assets/project-03.jpg
```

### Adding More Projects

To add more projects with images:

1. **Add HTML Structure:**
```html
<article class="project-item">
    <div class="project-header">
        <div class="project-number">04</div>
        <div class="project-meta">
            <h3 class="project-title">Your Project Name</h3>
            <span class="project-year">2024</span>
        </div>
    </div>
    <div class="project-details">
        <div class="project-image">
            <img src="assets/project-04.jpg" alt="Your Project Screenshot" class="project-img">
            <div class="project-placeholder">04</div>
        </div>
        <div class="project-info">
            <!-- Project description, tech, and links -->
        </div>
    </div>
</article>
```

2. **Add Corresponding Image:**
   - Save as `assets/project-04.jpg`
   - Follow the 16:9 aspect ratio
   - Optimize for web

### Design Benefits

**With Images:**
- ✅ Visual proof of your work
- ✅ Professional presentation
- ✅ Better user engagement
- ✅ Showcases UI/UX skills

**Layout Features:**
- ✅ 16:9 aspect ratio maintained
- ✅ Responsive design
- ✅ Hover effects for interactivity
- ✅ Graceful fallbacks
- ✅ Optimized loading

### Next Steps

1. Create or gather screenshots of your projects
2. Resize them to 16:9 aspect ratio (1920x1080px recommended)
3. Optimize file sizes
4. Save them in the `assets/` folder with the correct names
5. Test the portfolio to ensure images load properly

The layout is already optimized for images and will look professional whether images are present or not!
